<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.example</groupId>
  <artifactId>os-project</artifactId>
  <name>os-project</name>
  <version>1.0-SNAPSHOT</version>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.13.0</version>
        <configuration>
          <source>21</source>
          <target>21</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
              <mainClass>org.example.scheduler.MainApp</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer>
                  <mainClass>org.example.scheduler.MainApp</mainClass>
                </transformer>
              </transformers>
              <finalName>process-scheduler-simulator</finalName>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-maven-plugin</artifactId>
        <version>0.0.8</version>
        <executions>
          <execution>
            <id>default-cli</id>
            <configuration>
              <mainClass>org.example.osproject/org.example.scheduler.MainApp</mainClass>
              <launcher>app</launcher>
              <jlinkZipName>app</jlinkZipName>
              <jlinkImageName>app</jlinkImageName>
              <noManPages>true</noManPages>
              <stripDebug>true</stripDebug>
              <noHeaderFiles>true</noHeaderFiles>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.akathist.maven.plugins.launch4j</groupId>
        <artifactId>launch4j-maven-plugin</artifactId>
        <version>2.5.0</version>
        <executions>
          <execution>
            <id>l4j-clui</id>
            <phase>package</phase>
            <goals>
              <goal>launch4j</goal>
            </goals>
            <configuration>
              <headerType>gui</headerType>
              <jar>target/process-scheduler-simulator.jar</jar>
              <outfile>target/ProcessScheduler-Working.exe</outfile>
              <downloadUrl>https://www.oracle.com/java/technologies/downloads/</downloadUrl>
              <classPath>
                <mainClass>org.example.scheduler.MainApp</mainClass>
              </classPath>
              <jre>
                <bundledJre64Bit>false</bundledJre64Bit>
                <bundledJreAsFallback>false</bundledJreAsFallback>
                <minVersion>11</minVersion>
                <jdkPreference>preferJre</jdkPreference>
                <runtimeBits>64/32</runtimeBits>
              </jre>
              <versionInfo>
                <fileVersion>*******</fileVersion>
                <txtFileVersion>1.0.0</txtFileVersion>
                <fileDescription>Process Scheduler Simulator</fileDescription>
                <copyright>2025</copyright>
                <productVersion>*******</productVersion>
                <txtProductVersion>1.0.0</txtProductVersion>
                <productName>Process Scheduler Simulator</productName>
                <companyName>Example Organization</companyName>
                <internalName>ProcessScheduler</internalName>
                <originalFilename>ProcessScheduler.exe</originalFilename>
              </versionInfo>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>opentest4j</artifactId>
          <groupId>org.opentest4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-platform-commons</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.target>21</maven.compiler.target>
    <maven.compiler.source>21</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <javafx.version>21</javafx.version>
    <junit.version>5.10.2</junit.version>
  </properties>
</project>
