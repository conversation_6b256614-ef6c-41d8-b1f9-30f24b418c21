package org.example.scheduler;

    import java.util.ArrayList;
    import java.util.List;
    import java.util.Random;

    public class ProcessSchedulerModel {
        private static final int MAX_PROCESSES = 10; // 系统允许进程个数为n
        private PCB[] pcbArea; // 模拟进程控制块区域的数组
        private int pfree; // 定义指向空闲进程控制块队列的指针
        private int run;   // 定义指向正在运行进程的进程控制块的指针
        private ReadyQueuePointers ready; // 定义指向就绪队列的头指针head和尾指针tail

        private int nextProcessIdCounter = 1; // 用于唯一进程名（ID）
        private Random random = new Random();
        private StringBuilder logMessages;

        private int currentTime = 0; // 模拟时间计数器

        // 就绪队列头尾指针内部类
        private static class ReadyQueuePointers {
            int head = -1;
            int tail = -1;
        }

        public ProcessSchedulerModel() {
            pcbArea = new PCB[MAX_PROCESSES];
            logMessages = new StringBuilder();
            initializePcbAreaAndFreeList();
        }

        /**
         * 初始化PCB区域和空闲链表。所有PCB最初都链接到空闲链表中。
         */
        private void initializePcbAreaAndFreeList() {
            for (int i = 0; i < MAX_PROCESSES; i++) {
                pcbArea[i] = new PCB();
                pcbArea[i].reset();
                pcbArea[i].setNext(i + 1);
            }
            if (MAX_PROCESSES > 0) {
                pcbArea[MAX_PROCESSES - 1].setNext(-1);
                pfree = 0;
            } else {
                pfree = -1;
            }
            run = -1;
            ready = new ReadyQueuePointers();
            logMessage("PCB区域和空闲队列初始化完毕。");
        }

        /**
         * 将模拟重置到初始状态。
         */
        public void resetSimulation() {
            initializePcbAreaAndFreeList();
            nextProcessIdCounter = 1;
            currentTime = 0;
            logMessages.setLength(0);
            logMessage("模拟已重置。");
            // 确保所有PCB的JavaFX属性也被重置/更新
            for (PCB pcb : pcbArea) {
                pcb.updateProperties();
            }
        }

        private void logMessage(String message) {
            System.out.println(currentTime + ": " + message);
            logMessages.append(currentTime).append(": ").append(message).append("\n");
        }

        public String getLog() {
            return logMessages.toString();
        }

        /**
         * 创建一个新进程：分配PCB，填充信息，但不加到就绪队列。
         * @return 创建的PCB在pcbArea中的索引，若无空闲PCB则返回-1。
         */
        public int createNewProcessRaw() {
            if (pfree == -1) {
                logMessage("创建失败：没有空闲PCB。");
                return -1;
            }

            int newPcbIndex = pfree;
            pfree = pcbArea[newPcbIndex].next;

            PCB newPcb = pcbArea[newPcbIndex];
            newPcb.setName(nextProcessIdCounter++);
            newPcb.setStatus(ProcessState.READY);
            newPcb.setPri(random.nextInt(10) + 1); // 优先数1-10（大数代表高优先级）
            int burstTime = random.nextInt(10) + 1; // 剩余时间1-10
            newPcb.setTime(burstTime);
            newPcb.setInitialBurstTime(burstTime);
            newPcb.setArrivalTime(currentTime);
            newPcb.setNext(-1);

            newPcb.updateProperties();

            logMessage(String.format("创建原始进程 %d (索引 %d): 优先数=%d, 运行时间=%d。",
                    newPcb.name, newPcbIndex, newPcb.pri, newPcb.time));
            return newPcbIndex;
        }

        /**
         * 根据指定算法的规则，将先前创建的进程（通过其索引）添加到就绪队列。
         * @param pcbIndex 要添加的PCB索引
         * @param algorithm 调度算法
         */
        public void addProcessToReadyQueue(int pcbIndex, String algorithm) {
            if (pcbIndex < 0 || pcbIndex >= MAX_PROCESSES || pcbArea[pcbIndex].name == -1) {
                logMessage("添加到就绪队列失败：无效的PCB索引 " + pcbIndex);
                return;
            }

            PCB pcbToAdd = pcbArea[pcbIndex];
            pcbToAdd.setStatus(ProcessState.READY);
            logMessage("进程 " + pcbToAdd.name + " 加入就绪队列 (算法: " + algorithm + ")");

            switch (algorithm) {
                case "RR": // 时间片轮转
                    addProcessToReadyQueueFIFO(pcbIndex);
                    break;
                case "PRIORITY": // 优先数
                    addProcessToReadyQueueByPriority(pcbIndex);
                    break;
                case "SPF": // 最短进程优先
                case "SRTF": // 最短剩余时间优先
                    // SPF和SRTF都按time排序
                    addProcessToReadyQueueByTime(pcbIndex);
                    break;
                default:
                    logMessage("未知算法，使用FIFO加入就绪队列: " + algorithm);
                    addProcessToReadyQueueFIFO(pcbIndex);
                    break;
            }
            pcbToAdd.updateProperties();
        }

        /**
         * 按FIFO方式将PCB添加到就绪队列尾部。
         */
        private void addProcessToReadyQueueFIFO(int pcbIndex) {
            pcbArea[pcbIndex].setNext(-1);
            if (ready.head == -1) {
                ready.head = pcbIndex;
                ready.tail = pcbIndex;
            } else {
                pcbArea[ready.tail].setNext(pcbIndex);
                ready.tail = pcbIndex;
            }
        }

        /**
         * 按优先数（大数优先）将PCB插入到就绪队列的正确位置。
         */
        private void addProcessToReadyQueueByPriority(int pcbIndex) {
            PCB newPcb = pcbArea[pcbIndex];
            newPcb.setNext(-1);

            if (ready.head == -1) {
                ready.head = ready.tail = pcbIndex;
                return;
            }

            // 如果优先数比当前头大，插入头部
            if (newPcb.pri > pcbArea[ready.head].pri) {
                newPcb.setNext(ready.head);
                ready.head = pcbIndex;
                return;
            }

            // 遍历找到插入点
            int current = ready.head;
            int prev = -1;
            while (current != -1 && newPcb.pri <= pcbArea[current].pri) {
                prev = current;
                current = pcbArea[current].next;
            }

            // 插入到prev之后，current之前
            pcbArea[prev].setNext(pcbIndex);
            newPcb.setNext(current);

            if (current == -1) {
                ready.tail = pcbIndex;
            }
        }

        /**
         * 按执行时间（短者优先）将PCB插入到就绪队列的正确位置。
         */
        private void addProcessToReadyQueueByTime(int pcbIndex) {
            PCB newPcb = pcbArea[pcbIndex];
            newPcb.setNext(-1);

            if (ready.head == -1) {
                ready.head = ready.tail = pcbIndex;
                return;
            }

            // 如果时间比当前头短，插入头部
            if (newPcb.time < pcbArea[ready.head].time) {
                newPcb.setNext(ready.head);
                ready.head = pcbIndex;
                return;
            }

            int current = ready.head;
            int prev = -1;
            while (current != -1 && newPcb.time >= pcbArea[current].time) {
                prev = current;
                current = pcbArea[current].next;
            }

            // 插入到prev之后，current之前
            pcbArea[prev].setNext(pcbIndex);
            newPcb.setNext(current);

            if (current == -1) {
                ready.tail = pcbIndex;
            }
        }

        /**
         * 从就绪队列头部移除一个PCB。
         * @return 被移除的PCB索引，若队列为空返回-1。
         */
        private int removeFromReadyQueueHead() {
            if (ready.head == -1) return -1;

            int pcbIndex = ready.head;
            ready.head = pcbArea[pcbIndex].next;
            pcbArea[pcbIndex].setNext(-1);

            if (ready.head == -1) {
                ready.tail = -1;
            }
            return pcbIndex;
        }

        /**
         * 将PCB返回到空闲链表。
         * @param pcbIndex 要释放的PCB索引
         */
        private void returnPcbToFreeList(int pcbIndex) {
            PCB pcbToFree = pcbArea[pcbIndex];
            logMessage("进程 " + pcbToFree.name + " 已终止并返回到空闲列表。");
            pcbToFree.reset();
            pcbToFree.setNext(pfree);
            pfree = pcbIndex;
            pcbToFree.updateProperties();
        }

        /**
         * 模拟时间片轮转算法的一个步骤。
         * 每被调度1次，将其剩余运行时间－1。
         */
        public void stepRoundRobin() {
            currentTime++;
            logMessage("--- 时间片轮转调度 ---");

            // 处理当前正在运行的进程
            if (run != -1) {
                PCB currentRunning = pcbArea[run];
                currentRunning.time--;
                currentRunning.updateProperties();
                logMessage(String.format("进程 %d 运行一个时间片，剩余时间: %d", currentRunning.name, currentRunning.time));

                if (currentRunning.time <= 0) {
                    logMessage(String.format("进程 %d 完成运行。", currentRunning.name));
                    currentRunning.setStatus(ProcessState.TERMINATED);
                    returnPcbToFreeList(run);
                    run = -1;
                } else {
                    // 时间片到，未完成，返回就绪队列
                    logMessage(String.format("进程 %d 时间片到，返回就绪队列。", currentRunning.name));
                    currentRunning.setStatus(ProcessState.READY);
                    addProcessToReadyQueueFIFO(run);
                    run = -1;
                }
            }

            // 若无进程运行，则从就绪队列调度新进程
            if (run == -1 && ready.head != -1) {
                run = removeFromReadyQueueHead();
                if (run != -1) {
                    pcbArea[run].setStatus(ProcessState.RUNNING);
                    pcbArea[run].updateProperties();
                    logMessage(String.format("调度进程 %d 运行。优先数: %d, 剩余时间: %d",
                            pcbArea[run].name, pcbArea[run].pri, pcbArea[run].time));
                }
            } else if (run == -1 && ready.head == -1) {
                logMessage("就绪队列为空，无进程可调度。");
            }
        }

        /**
         * 模拟优先数调度算法的一个步骤。
         */
        public void stepPriority() {
            currentTime++;
            logMessage("--- 优先数调度 ---");

            // 处理当前正在运行的进程
            if (run != -1) {
                PCB currentRunning = pcbArea[run];
                currentRunning.time--;
                currentRunning.pri--;
                if (currentRunning.pri < 0) currentRunning.pri = 0;
                currentRunning.updateProperties();

                logMessage(String.format("进程 %d 运行，剩余时间: %d, 新优先数: %d",
                        currentRunning.name, currentRunning.time, currentRunning.pri));

                if (currentRunning.time <= 0) {
                    logMessage(String.format("进程 %d 完成运行。", currentRunning.name));
                    currentRunning.setStatus(ProcessState.TERMINATED);
                    returnPcbToFreeList(run);
                    run = -1;
                } else {
                    // 未完成，以新优先数返回就绪队列
                    logMessage(String.format("进程 %d 未完成，以新优先数返回就绪队列。", currentRunning.name));
                    currentRunning.setStatus(ProcessState.READY);
                    addProcessToReadyQueueByPriority(run);
                    run = -1;
                }
            }

            // 若无进程运行，则从就绪队列调度新进程
            if (run == -1 && ready.head != -1) {
                run = removeFromReadyQueueHead();
                if (run != -1) {
                    pcbArea[run].setStatus(ProcessState.RUNNING);
                    pcbArea[run].updateProperties();
                    logMessage(String.format("调度进程 %d 运行。优先数: %d, 剩余时间: %d",
                            pcbArea[run].name, pcbArea[run].pri, pcbArea[run].time));
                }
            } else if (run == -1 && ready.head == -1) {
                logMessage("就绪队列为空，无进程可调度。");
            }
        }

        /**
         * 模拟最短进程优先（非抢占）调度算法的一个步骤。
         * 调度到的进程就可以运行完
         */
        public void stepSPF() {
            // 若有进程运行，说明流程有误
            boolean timeAdvancedByProcess = false;
            logMessage("--- 最短进程优先调度 (非抢占) ---");

            if (run != -1) {
                logMessage("错误：SPF调度步骤开始时不应有正在运行的进程（run应为-1）。");
            }

            if (ready.head != -1) {
                run = removeFromReadyQueueHead();

                if (run != -1) {
                    PCB selectedPcb = pcbArea[run];
                    selectedPcb.setStatus(ProcessState.RUNNING);
                    selectedPcb.updateProperties();
                    logMessage(String.format("调度进程 %d 运行。总运行时间: %d",
                            selectedPcb.name, selectedPcb.time));

                    // 模拟其全部运行
                    currentTime += selectedPcb.time;
                    timeAdvancedByProcess = true;
                    selectedPcb.setTime(0);
                    selectedPcb.setStatus(ProcessState.TERMINATED);
                    selectedPcb.updateProperties();
                    logMessage(String.format("进程 %d 完成运行。结束时间: %d", selectedPcb.name, currentTime));
                    returnPcbToFreeList(run);
                    run = -1;
                }
            } else {
                logMessage("就绪队列为空，无进程可调度。");
            }
            if (!timeAdvancedByProcess) currentTime++;
        }

        /**
         * 模拟最短剩余时间优先（抢占）调度算法的一个步骤。
         */
        public void stepSRTF() {
            currentTime++;
            logMessage("--- 最短剩余时间优先调度 (抢占) ---");

            // 1. 若有进程正在运行，执行一个时间单位
            if (run != -1) {
                PCB currentRunning = pcbArea[run];
                currentRunning.time--;
                currentRunning.updateProperties();
                logMessage(String.format("进程 %d 运行一个单位，剩余时间: %d", currentRunning.name, currentRunning.time));

                if (currentRunning.time <= 0) {
                    logMessage(String.format("进程 %d 完成运行。", currentRunning.name));
                    currentRunning.setStatus(ProcessState.TERMINATED);
                    returnPcbToFreeList(run);
                    run = -1;
                }
            }

            // 2. 决策：选择剩余时间最短的进程运行
            int shortestInReadyIdx = -1;
            if (ready.head != -1) {
                shortestInReadyIdx = ready.head;
            }

            PCB candidateFromReady = (shortestInReadyIdx != -1) ? pcbArea[shortestInReadyIdx] : null;
            PCB currentRunningPcb = (run != -1) ? pcbArea[run] : null;

            if (currentRunningPcb != null && (candidateFromReady == null || currentRunningPcb.time <= candidateFromReady.time)) {
                // 当前进程继续持有CPU
                logMessage(String.format("进程 %d 继续持有CPU。", currentRunningPcb.name));
            } else if (candidateFromReady != null && (currentRunningPcb == null || candidateFromReady.time < currentRunningPcb.time)) {
                // 抢占或新进程获得CPU
                if (currentRunningPcb != null) {
                    logMessage(String.format("抢占！进程 %d (剩余 %d) 被进程 %d (剩余 %d) 抢占。",
                            currentRunningPcb.name, currentRunningPcb.time,
                            candidateFromReady.name, candidateFromReady.time));
                    currentRunningPcb.setStatus(ProcessState.READY);
                    addProcessToReadyQueueByTime(run);
                } else {
                    logMessage(String.format("就绪队列中进程 %d (剩余 %d) 开始运行。",
                            candidateFromReady.name, candidateFromReady.time));
                }
                run = removeFromReadyQueueHead();
                pcbArea[run].setStatus(ProcessState.RUNNING);
                pcbArea[run].updateProperties();
                logMessage(String.format("调度新进程 %d 运行。剩余时间: %d", pcbArea[run].name, pcbArea[run].time));
            } else {
                logMessage("就绪队列为空，无进程可调度。");
            }
        }

        /**
         * 动态地（随机）生成一个新进程并添加到相应的就绪队列。
         * @param currentAlgorithm 当前选中的算法
         */
        public void dynamicallyAddProcess(String currentAlgorithm) {
            // 每个时间单位有25%概率添加新进程（若有空闲PCB）
            if (pfree != -1 && random.nextDouble() < 0.25) {
                int newPcbIdx = createNewProcessRaw();
                if (newPcbIdx != -1) {
                    logMessage("动态生成新进程 " + pcbArea[newPcbIdx].name + " 在时间 " + currentTime);
                    addProcessToReadyQueue(newPcbIdx, currentAlgorithm);
                }
            }
        }

        // --- UI相关Getter方法 ---
        public PCB[] getPcbArray() { return pcbArea; }

        public List<PCB> getReadyQueueContents() {
            List<PCB> qContents = new ArrayList<>();
            int currentIdx = ready.head;
            while (currentIdx != -1) {
                qContents.add(pcbArea[currentIdx]);
                currentIdx = pcbArea[currentIdx].next;
            }
            return qContents;
        }

        public PCB getRunningProcess() {
            if (run != -1) {
                return pcbArea[run];
            }
            return null;
        }

        public int getCurrentTime() { return currentTime; }
    }