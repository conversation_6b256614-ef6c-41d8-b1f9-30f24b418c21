package org.example.scheduler;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.example.scheduler.SchedulerController;

import java.io.IOException;
import java.util.Objects;

public class MainApp extends Application {

    @Override
    public void start(Stage primaryStage) throws IOException {
        FXMLLoader loader = new FXMLLoader(Objects.requireNonNull(getClass().getResource("/org/example/osproject/scheduler-view.fxml")));
        VBox root = loader.load();

        Scene scene = new Scene(root, 950, 750); // 可以根据需要调整窗口大小
        primaryStage.setTitle("单处理器进程调度模拟器 (JavaFX)");
        primaryStage.setScene(scene);

        SchedulerController controller = loader.getController();
        controller.setStage(primaryStage);

        primaryStage.setOnCloseRequest(event -> {
            // 当用户尝试关闭窗口时，确保自动运行线程停止
            if (controller != null) {
                controller.stopAutoRun(); // 调用controller中公开的stopAutoRun方法
            }
        });

        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}